"""
Jina AI 嵌入模型的单元测试
"""

import pytest
import asyncio
import sys
import os
from unittest.mock import Mock, patch, AsyncMock

# 添加父目录到路径以便导入 jina_embeddings
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from jina_embeddings import JinaEmbeddings, create_jina_embeddings


class TestJinaEmbeddings:
    """Jina AI 嵌入模型测试类"""
    
    def setup_method(self):
        """测试前的设置"""
        self.api_key = "jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR"
        self.model_name = "jina-embeddings-v3"
        self.embeddings = JinaEmbeddings(
            api_key=self.api_key,
            model_name=self.model_name,
            batch_size=2,  # 小批次用于测试
            timeout=10,
            max_retries=1
        )
    
    def test_initialization(self):
        """测试初始化"""
        assert self.embeddings.api_key == self.api_key
        assert self.embeddings.model_name == self.model_name
        assert self.embeddings.batch_size == 2
        assert self.embeddings.timeout == 10
        assert self.embeddings.max_retries == 1
    
    def test_initialization_without_api_key(self):
        """测试没有API密钥的初始化"""
        with pytest.raises(ValueError, match="API 密钥不能为空"):
            JinaEmbeddings(api_key="")
    
    def test_prepare_headers(self):
        """测试请求头准备"""
        headers = self.embeddings._prepare_headers()
        expected_headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        assert headers == expected_headers
    
    def test_prepare_payload(self):
        """测试请求负载准备"""
        texts = ["文本1", "文本2"]
        payload = self.embeddings._prepare_payload(texts)
        expected_payload = {
            "model": self.model_name,
            "task": "retrieval.query",
            "input": texts
        }
        assert payload == expected_payload
    
    def test_handle_response_success(self):
        """测试成功响应处理"""
        response_data = {
            "data": [
                {"embedding": [0.1, 0.2, 0.3]},
                {"embedding": [0.4, 0.5, 0.6]}
            ]
        }
        embeddings = self.embeddings._handle_response(response_data)
        expected_embeddings = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]]
        assert embeddings == expected_embeddings
    
    def test_handle_response_error(self):
        """测试错误响应处理"""
        # 缺少 data 字段
        response_data = {"error": "Invalid request"}
        with pytest.raises(ValueError, match="API 响应格式错误"):
            self.embeddings._handle_response(response_data)
        
        # 缺少 embedding 字段
        response_data = {"data": [{"index": 0}]}
        with pytest.raises(ValueError, match="嵌入数据格式错误"):
            self.embeddings._handle_response(response_data)
    
    @patch('httpx.Client')
    def test_embed_documents_success(self, mock_client):
        """测试同步文档嵌入成功"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.json.return_value = {
            "data": [
                {"embedding": [0.1, 0.2]},
                {"embedding": [0.3, 0.4]}
            ]
        }
        mock_response.raise_for_status.return_value = None
        
        mock_client_instance = Mock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__enter__.return_value = mock_client_instance
        
        texts = ["文本1", "文本2"]
        embeddings = self.embeddings.embed_documents(texts)
        
        assert embeddings == [[0.1, 0.2], [0.3, 0.4]]
        mock_client_instance.post.assert_called_once()
    
    def test_embed_documents_empty_list(self):
        """测试空文本列表"""
        embeddings = self.embeddings.embed_documents([])
        assert embeddings == []
    
    def test_embed_query(self):
        """测试单个查询嵌入"""
        with patch.object(self.embeddings, 'embed_documents') as mock_embed:
            mock_embed.return_value = [[0.1, 0.2, 0.3]]
            
            result = self.embeddings.embed_query("测试查询")
            
            assert result == [0.1, 0.2, 0.3]
            mock_embed.assert_called_once_with(["测试查询"])
    
    @pytest.mark.asyncio
    async def test_aembed_documents_success(self):
        """测试异步文档嵌入成功"""
        with patch('httpx.AsyncClient') as mock_client:
            # 模拟成功响应
            mock_response = Mock()
            mock_response.json.return_value = {
                "data": [
                    {"embedding": [0.1, 0.2]},
                    {"embedding": [0.3, 0.4]}
                ]
            }
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client_instance.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            
            texts = ["文本1", "文本2"]
            embeddings = await self.embeddings.aembed_documents(texts)
            
            assert embeddings == [[0.1, 0.2], [0.3, 0.4]]
            mock_client_instance.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_aembed_documents_empty_list(self):
        """测试异步空文本列表"""
        embeddings = await self.embeddings.aembed_documents([])
        assert embeddings == []
    
    @pytest.mark.asyncio
    async def test_aembed_query(self):
        """测试异步单个查询嵌入"""
        with patch.object(self.embeddings, 'aembed_documents') as mock_embed:
            mock_embed.return_value = [[0.1, 0.2, 0.3]]
            
            result = await self.embeddings.aembed_query("测试查询")
            
            assert result == [0.1, 0.2, 0.3]
            mock_embed.assert_called_once_with(["测试查询"])
    
    def test_batch_processing(self):
        """测试批处理功能"""
        with patch.object(self.embeddings, '_embed_batch_sync') as mock_batch:
            mock_batch.side_effect = [
                [[0.1, 0.2], [0.3, 0.4]],  # 第一批
                [[0.5, 0.6], [0.7, 0.8]],  # 第二批
                [[0.9, 1.0]]               # 第三批
            ]
            
            # 5个文本，批次大小为2，应该分成3批
            texts = ["文本1", "文本2", "文本3", "文本4", "文本5"]
            embeddings = self.embeddings.embed_documents(texts)
            
            expected_embeddings = [
                [0.1, 0.2], [0.3, 0.4], [0.5, 0.6], [0.7, 0.8], [0.9, 1.0]
            ]
            assert embeddings == expected_embeddings
            assert mock_batch.call_count == 3


class TestCreateJinaEmbeddings:
    """测试工厂函数"""
    
    def test_create_jina_embeddings(self):
        """测试创建函数"""
        api_key = "test-key"
        model_name = "test-model"
        
        embeddings = create_jina_embeddings(
            api_key=api_key,
            model_name=model_name,
            batch_size=50
        )
        
        assert isinstance(embeddings, JinaEmbeddings)
        assert embeddings.api_key == api_key
        assert embeddings.model_name == model_name
        assert embeddings.batch_size == 50


class TestIntegration:
    """集成测试"""
    
    def test_ragas_compatibility(self):
        """测试与 RAGAS 的兼容性"""
        from ragas.embeddings.base import BaseRagasEmbeddings
        
        embeddings = create_jina_embeddings(api_key="test-key")
        
        # 检查是否继承自正确的基类
        assert isinstance(embeddings, BaseRagasEmbeddings)
        
        # 检查是否实现了必要的方法
        assert hasattr(embeddings, 'embed_documents')
        assert hasattr(embeddings, 'embed_query')
        assert hasattr(embeddings, 'aembed_documents')
        assert hasattr(embeddings, 'aembed_query')
        assert hasattr(embeddings, 'set_run_config')


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
