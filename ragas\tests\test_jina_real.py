"""
Jina AI 嵌入模型的真实 API 测试
这个测试使用真实的 Jina AI API 进行测试
"""

import os
import asyncio
import sys

# 添加父目录到路径以便导入 jina_embeddings
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from jina_embeddings import create_jina_embeddings


def test_real_jina_api():
    """测试真实的 Jina AI API"""
    print("🧪 测试真实的 Jina AI API...")
    
    # 使用您在测试文件中设置的 API 密钥
    api_key = "jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR"
    
    try:
        # 创建嵌入实例
        embeddings = create_jina_embeddings(
            api_key=api_key,
            model_name="jina-embeddings-v3",
            batch_size=5,
            timeout=30
        )
        
        # 测试文本
        test_texts = [
            "人工智能正在改变世界",
            "机器学习是AI的重要分支",
            "深度学习需要大量数据"
        ]
        
        print(f"📝 测试嵌入 {len(test_texts)} 个文本...")
        
        # 同步嵌入
        vectors = embeddings.embed_documents(test_texts)
        
        print(f"✅ 成功获得 {len(vectors)} 个嵌入向量")
        print(f"   向量维度: {len(vectors[0])}")
        
        # 测试单个查询
        query = "什么是人工智能？"
        query_vector = embeddings.embed_query(query)
        print(f"✅ 查询嵌入成功，维度: {len(query_vector)}")
        
        # 计算相似度
        import numpy as np
        
        def cosine_similarity(a, b):
            return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
        
        similarity = cosine_similarity(query_vector, vectors[0])
        print(f"📊 查询与第一个文本的相似度: {similarity:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_real_jina_api_async():
    """测试真实的 Jina AI API - 异步版本"""
    print("\n🔄 测试异步 Jina AI API...")
    
    api_key = "jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR"
    
    try:
        embeddings = create_jina_embeddings(
            api_key=api_key,
            model_name="jina-embeddings-v3",
            batch_size=5,
            timeout=30
        )
        
        test_texts = [
            "自然语言处理技术发展迅速",
            "计算机视觉应用广泛",
            "语音识别准确率不断提升"
        ]
        
        print(f"📝 异步测试嵌入 {len(test_texts)} 个文本...")
        
        # 异步嵌入
        vectors = await embeddings.aembed_documents(test_texts)
        
        print(f"✅ 异步成功获得 {len(vectors)} 个嵌入向量")
        print(f"   向量维度: {len(vectors[0])}")
        
        # 异步查询
        query = "NLP技术有哪些应用？"
        query_vector = await embeddings.aembed_query(query)
        print(f"✅ 异步查询嵌入成功，维度: {len(query_vector)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        return False


def test_batch_processing():
    """测试批处理功能"""
    print("\n📦 测试批处理功能...")
    
    api_key = "jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR"
    
    try:
        embeddings = create_jina_embeddings(
            api_key=api_key,
            batch_size=3,  # 小批次用于测试
            timeout=30
        )
        
        # 创建较多文本来测试批处理
        large_text_list = [
            f"这是第 {i} 个测试文本，用于验证批处理功能。"
            for i in range(10)
        ]
        
        print(f"📝 批处理测试 {len(large_text_list)} 个文本...")
        
        vectors = embeddings.embed_documents(large_text_list)
        
        print(f"✅ 批处理成功，获得 {len(vectors)} 个向量")
        print(f"   每个向量维度: {len(vectors[0])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批处理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 Jina AI 嵌入模型真实 API 测试")
    print("=" * 50)
    
    # 同步测试
    sync_result = test_real_jina_api()
    
    # 异步测试
    async_result = asyncio.run(test_real_jina_api_async())
    
    # 批处理测试
    batch_result = test_batch_processing()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   同步测试: {'✅ 通过' if sync_result else '❌ 失败'}")
    print(f"   异步测试: {'✅ 通过' if async_result else '❌ 失败'}")
    print(f"   批处理测试: {'✅ 通过' if batch_result else '❌ 失败'}")
    
    if all([sync_result, async_result, batch_result]):
        print("\n🎉 所有真实 API 测试都通过了！")
        print("💡 您的 Jina AI 嵌入实现已经可以正常工作了！")
    else:
        print("\n⚠️  部分测试失败，请检查 API 密钥和网络连接")


if __name__ == "__main__":
    main()
