"""
Jina AI 嵌入与 RAGAS 的最终演示
展示完整的评估流程
"""

import sys
import os
import time

# 添加父目录到路径以便导入 jina_embeddings
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ragas import SingleTurnSample
from ragas.metrics import SemanticSimilarity
from jina_embeddings import create_jina_embeddings


def create_test_dataset():
    """创建测试数据集"""
    return [
        SingleTurnSample(
            user_input="什么是人工智能？",
            response="人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。这包括学习、推理、问题解决、感知和语言理解等能力。AI系统可以分为弱人工智能和强人工智能两类。",
            reference="人工智能是模拟人类智能的计算机系统，能够学习、推理和解决问题。"
        ),
        SingleTurnSample(
            user_input="机器学习和深度学习有什么区别？",
            response="机器学习是人工智能的一个子集，它使计算机能够从数据中学习而无需明确编程。深度学习是机器学习的一个子集，使用具有多层的神经网络来模拟人脑的工作方式。深度学习在图像识别、自然语言处理等领域表现出色。",
            reference="机器学习是AI的分支，让计算机从数据学习。深度学习是机器学习的子集，使用多层神经网络。"
        ),
        SingleTurnSample(
            user_input="自然语言处理的主要应用有哪些？",
            response="自然语言处理（NLP）有许多重要应用，包括：1）机器翻译，如Google翻译；2）情感分析，用于社交媒体监控；3）聊天机器人和虚拟助手；4）文本摘要和信息提取；5）语音识别和语音合成；6）问答系统；7）搜索引擎优化等。",
            reference="NLP应用包括机器翻译、情感分析、聊天机器人、语音识别、文本摘要等。"
        ),
        SingleTurnSample(
            user_input="什么是神经网络？",
            response="神经网络是一种受生物神经系统启发的计算模型，由相互连接的节点（神经元）组成。每个节点接收输入，进行处理，然后传递输出到其他节点。神经网络通过调整连接权重来学习模式和关系。",
            reference="神经网络是模拟生物神经系统的计算模型，由相互连接的节点组成。"
        ),
        SingleTurnSample(
            user_input="计算机视觉技术有哪些应用？",
            response="计算机视觉技术广泛应用于多个领域：1）医疗影像分析，如X光和MRI诊断；2）自动驾驶汽车的环境感知；3）人脸识别和身份验证；4）工业质量检测；5）增强现实和虚拟现实；6）安防监控系统；7）农业作物监测等。",
            reference="计算机视觉应用于医疗诊断、自动驾驶、人脸识别、工业检测等领域。"
        )
    ]


def run_evaluation_demo():
    """运行评估演示"""
    print("🚀 Jina AI 嵌入 + RAGAS 评估演示")
    print("=" * 60)
    
    # 1. 创建 Jina AI 嵌入实例
    print("📦 步骤 1: 创建 Jina AI 嵌入实例...")
    api_key = "jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR"
    
    embeddings = create_jina_embeddings(
        api_key=api_key,
        model_name="jina-embeddings-v3",
        task="retrieval.query",
        batch_size=10,
        timeout=30,
        max_retries=3
    )
    print("✅ Jina AI 嵌入实例创建成功")
    
    # 2. 创建 RAGAS 指标
    print("\n📊 步骤 2: 创建 RAGAS 评估指标...")
    semantic_similarity = SemanticSimilarity(embeddings=embeddings)
    print("✅ 语义相似度指标创建成功")
    
    # 3. 准备测试数据
    print("\n📝 步骤 3: 准备测试数据...")
    test_samples = create_test_dataset()
    print(f"✅ 创建了 {len(test_samples)} 个测试样本")
    
    # 4. 运行评估
    print("\n🔍 步骤 4: 运行语义相似度评估...")
    start_time = time.time()
    
    scores = []
    for i, sample in enumerate(test_samples, 1):
        print(f"   评估样本 {i}/{len(test_samples)}...", end=" ")
        score = semantic_similarity.single_turn_score(sample)
        scores.append(score)
        print(f"得分: {score:.4f}")
    
    end_time = time.time()
    evaluation_time = end_time - start_time
    
    # 5. 分析结果
    print(f"\n📈 步骤 5: 分析评估结果...")
    avg_score = sum(scores) / len(scores)
    max_score = max(scores)
    min_score = min(scores)
    
    print(f"   📊 评估统计:")
    print(f"      样本数量: {len(scores)}")
    print(f"      平均得分: {avg_score:.4f}")
    print(f"      最高得分: {max_score:.4f}")
    print(f"      最低得分: {min_score:.4f}")
    print(f"      评估耗时: {evaluation_time:.2f} 秒")
    print(f"      平均每样本: {evaluation_time/len(scores):.2f} 秒")
    
    # 6. 性能分析
    print(f"\n⚡ 步骤 6: 性能分析...")
    
    # 计算嵌入性能
    test_texts = [sample.response for sample in test_samples]
    embed_start = time.time()
    vectors = embeddings.embed_documents(test_texts)
    embed_end = time.time()
    embed_time = embed_end - embed_start
    
    print(f"   🚀 嵌入性能:")
    print(f"      文本数量: {len(test_texts)}")
    print(f"      嵌入维度: {len(vectors[0])}")
    print(f"      嵌入耗时: {embed_time:.2f} 秒")
    print(f"      处理速度: {len(test_texts)/embed_time:.2f} 文本/秒")
    
    # 7. 质量分析
    print(f"\n🎯 步骤 7: 质量分析...")
    
    # 分析得分分布
    high_quality = sum(1 for score in scores if score >= 0.8)
    medium_quality = sum(1 for score in scores if 0.6 <= score < 0.8)
    low_quality = sum(1 for score in scores if score < 0.6)
    
    print(f"   📋 质量分布:")
    print(f"      高质量 (≥0.8): {high_quality} 个 ({high_quality/len(scores)*100:.1f}%)")
    print(f"      中等质量 (0.6-0.8): {medium_quality} 个 ({medium_quality/len(scores)*100:.1f}%)")
    print(f"      低质量 (<0.6): {low_quality} 个 ({low_quality/len(scores)*100:.1f}%)")
    
    # 8. 总结
    print(f"\n🎉 步骤 8: 评估总结...")
    print(f"   ✅ 成功完成 {len(test_samples)} 个样本的语义相似度评估")
    print(f"   ✅ 使用 Jina AI 嵌入模型 (维度: {len(vectors[0])})")
    print(f"   ✅ 平均语义相似度: {avg_score:.4f}")
    print(f"   ✅ 总评估时间: {evaluation_time:.2f} 秒")
    
    if avg_score >= 0.7:
        print(f"   🌟 评估结果良好！响应与参考答案具有较高的语义相似性")
    elif avg_score >= 0.5:
        print(f"   📊 评估结果中等，响应与参考答案有一定的语义相似性")
    else:
        print(f"   ⚠️  评估结果较低，可能需要改进响应质量")
    
    print("\n" + "=" * 60)
    print("🎊 演示完成！")
    print("\n💡 下一步建议:")
    print("   1. 在更大的数据集上进行评估")
    print("   2. 结合其他 RAGAS 指标进行综合评估")
    print("   3. 根据评估结果优化模型或提示")
    print("   4. 建立持续评估流程")


if __name__ == "__main__":
    run_evaluation_demo()
