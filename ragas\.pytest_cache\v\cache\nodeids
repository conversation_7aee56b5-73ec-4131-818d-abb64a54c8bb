["test_jina_embeddings.py::TestCreateJinaEmbeddings::test_create_jina_embeddings", "test_jina_embeddings.py::TestIntegration::test_ragas_compatibility", "test_jina_embeddings.py::TestJinaEmbeddings::test_aembed_documents_empty_list", "test_jina_embeddings.py::TestJinaEmbeddings::test_aembed_documents_success", "test_jina_embeddings.py::TestJinaEmbeddings::test_aembed_query", "test_jina_embeddings.py::TestJinaEmbeddings::test_batch_processing", "test_jina_embeddings.py::TestJinaEmbeddings::test_embed_documents_empty_list", "test_jina_embeddings.py::TestJinaEmbeddings::test_embed_documents_success", "test_jina_embeddings.py::TestJinaEmbeddings::test_embed_query", "test_jina_embeddings.py::TestJinaEmbeddings::test_handle_response_error", "test_jina_embeddings.py::TestJinaEmbeddings::test_handle_response_success", "test_jina_embeddings.py::TestJinaEmbeddings::test_initialization", "test_jina_embeddings.py::TestJinaEmbeddings::test_initialization_without_api_key", "test_jina_embeddings.py::TestJinaEmbeddings::test_prepare_headers", "test_jina_embeddings.py::TestJinaEmbeddings::test_prepare_payload"]