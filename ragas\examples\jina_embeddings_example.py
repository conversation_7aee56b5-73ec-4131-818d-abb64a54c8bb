"""
Jina AI 嵌入模型使用示例
演示如何在 RAGAS 框架中使用自定义的 Jina AI 嵌入模型
"""

import asyncio
import os
import sys
from typing import List

# 添加父目录到路径以便导入 jina_embeddings
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ragas import SingleTurnSample, evaluate
from ragas.metrics import SemanticSimilarity, ContextPrecision
from ragas.run_config import RunConfig
from jina_embeddings import JinaEmbeddings, create_jina_embeddings


def setup_jina_embeddings() -> JinaEmbeddings:
    """
    设置 Jina AI 嵌入模型
    
    返回:
        配置好的 JinaEmbeddings 实例
    """
    # 从环境变量获取 API 密钥，或者直接设置
    api_key = os.getenv("JINA_API_KEY", "your-jina-api-key-here")
    
    if api_key == "your-jina-api-key-here":
        print("⚠️  请设置您的 Jina AI API 密钥")
        print("方法1: 设置环境变量 JINA_API_KEY")
        print("方法2: 直接在代码中替换 api_key 变量")
        return None
    
    # 创建 Jina AI 嵌入实例
    embeddings = create_jina_embeddings(
        api_key=api_key,
        model_name="jina-embeddings-v3",  # 可以选择其他模型
        batch_size=50,  # 批处理大小
        timeout=30,     # 超时时间
        max_retries=3   # 最大重试次数
    )
    
    print(f"✅ 成功创建 Jina AI 嵌入模型: {embeddings.model_name}")
    return embeddings


def test_basic_embedding():
    """测试基本的嵌入功能"""
    print("\n🧪 测试基本嵌入功能...")
    
    embeddings = setup_jina_embeddings()
    if not embeddings:
        return
    
    # 测试文本
    test_texts = [
        "人工智能正在改变世界",
        "机器学习是AI的重要分支",
        "深度学习模型需要大量数据训练"
    ]
    
    try:
        # 同步嵌入
        print("📝 进行同步嵌入...")
        sync_embeddings = embeddings.embed_documents(test_texts)
        print(f"✅ 同步嵌入完成，获得 {len(sync_embeddings)} 个向量")
        print(f"   向量维度: {len(sync_embeddings[0])}")
        
        # 单个查询嵌入
        query = "什么是人工智能？"
        query_embedding = embeddings.embed_query(query)
        print(f"✅ 查询嵌入完成，向量维度: {len(query_embedding)}")
        
    except Exception as e:
        print(f"❌ 嵌入测试失败: {e}")


async def test_async_embedding():
    """测试异步嵌入功能"""
    print("\n🔄 测试异步嵌入功能...")
    
    embeddings = setup_jina_embeddings()
    if not embeddings:
        return
    
    # 测试文本
    test_texts = [
        "自然语言处理是AI的核心技术",
        "计算机视觉让机器能够理解图像",
        "语音识别技术不断进步"
    ]
    
    try:
        # 异步嵌入
        print("📝 进行异步嵌入...")
        async_embeddings = await embeddings.aembed_documents(test_texts)
        print(f"✅ 异步嵌入完成，获得 {len(async_embeddings)} 个向量")
        
        # 异步查询嵌入
        query = "NLP技术的应用有哪些？"
        query_embedding = await embeddings.aembed_query(query)
        print(f"✅ 异步查询嵌入完成，向量维度: {len(query_embedding)}")
        
    except Exception as e:
        print(f"❌ 异步嵌入测试失败: {e}")


def test_with_ragas_metrics():
    """测试与 RAGAS 指标的集成"""
    print("\n📊 测试与 RAGAS 指标集成...")
    
    embeddings = setup_jina_embeddings()
    if not embeddings:
        return
    
    try:
        # 创建语义相似度指标，使用自定义嵌入
        semantic_similarity = SemanticSimilarity(embeddings=embeddings)
        
        # 创建测试样本
        sample = SingleTurnSample(
            user_input="请解释什么是机器学习",
            response="机器学习是人工智能的一个分支，它使计算机能够从数据中学习并做出预测或决策，而无需明确编程。",
            reference="机器学习是AI的子领域，通过算法让计算机从数据中自动学习模式和规律。"
        )
        
        # 计算语义相似度
        print("📝 计算语义相似度...")
        similarity_score = semantic_similarity.single_turn_score(sample)
        print(f"✅ 语义相似度得分: {similarity_score:.4f}")
        
    except Exception as e:
        print(f"❌ RAGAS 指标测试失败: {e}")


def test_batch_processing():
    """测试批处理功能"""
    print("\n📦 测试批处理功能...")
    
    embeddings = setup_jina_embeddings()
    if not embeddings:
        return
    
    # 创建大量测试文本
    large_text_list = [
        f"这是第 {i} 个测试文本，用于验证批处理功能的效果。"
        for i in range(1, 101)  # 100个文本
    ]
    
    try:
        print(f"📝 处理 {len(large_text_list)} 个文本...")
        batch_embeddings = embeddings.embed_documents(large_text_list)
        print(f"✅ 批处理完成，获得 {len(batch_embeddings)} 个向量")
        
    except Exception as e:
        print(f"❌ 批处理测试失败: {e}")


def demonstrate_configuration():
    """演示不同的配置选项"""
    print("\n⚙️  演示配置选项...")
    
    api_key = os.getenv("JINA_API_KEY", "your-jina-api-key-here")
    if api_key == "your-jina-api-key-here":
        print("⚠️  需要设置 API 密钥才能运行配置演示")
        return
    
    # 不同的配置示例
    configs = [
        {
            "name": "高性能配置",
            "batch_size": 100,
            "timeout": 60,
            "max_retries": 5
        },
        {
            "name": "保守配置",
            "batch_size": 20,
            "timeout": 15,
            "max_retries": 2
        }
    ]
    
    for config in configs:
        print(f"\n📋 {config['name']}:")
        try:
            embeddings = JinaEmbeddings(
                api_key=api_key,
                model_name="jina-embeddings-v3",
                batch_size=config["batch_size"],
                timeout=config["timeout"],
                max_retries=config["max_retries"]
            )
            print(f"   ✅ 批处理大小: {embeddings.batch_size}")
            print(f"   ✅ 超时时间: {embeddings.timeout}秒")
            print(f"   ✅ 最大重试: {embeddings.max_retries}次")
            
        except Exception as e:
            print(f"   ❌ 配置失败: {e}")


def main():
    """主函数，运行所有测试"""
    print("🚀 Jina AI 嵌入模型测试开始")
    print("=" * 50)
    
    # 基本功能测试
    test_basic_embedding()
    
    # 异步功能测试
    asyncio.run(test_async_embedding())
    
    # RAGAS 集成测试
    test_with_ragas_metrics()
    
    # 批处理测试
    test_batch_processing()
    
    # 配置演示
    demonstrate_configuration()
    
    print("\n" + "=" * 50)
    print("🎉 所有测试完成！")


if __name__ == "__main__":
    main()
