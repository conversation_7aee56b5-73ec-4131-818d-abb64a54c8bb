"""
测试运行脚本
方便运行所有测试
"""

import os
import sys
import subprocess


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}...")
    print(f"命令: {command}")
    print("-" * 50)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, cwd="tests")
        
        if result.returncode == 0:
            print("✅ 成功")
            if result.stdout:
                print(result.stdout)
        else:
            print("❌ 失败")
            if result.stderr:
                print("错误信息:")
                print(result.stderr)
            if result.stdout:
                print("输出信息:")
                print(result.stdout)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Jina AI 嵌入模型测试套件")
    print("=" * 60)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 测试列表
    tests = [
        ("python -m pytest test_jina_embeddings.py -v", "单元测试"),
        ("python test_jina_api_direct.py", "直接 API 测试"),
        ("python simple_integration_test.py", "简单集成测试"),
        ("python test_jina_real.py", "真实 API 测试"),
    ]
    
    results = []
    
    # 运行所有测试
    for command, description in tests:
        success = run_command(command, description)
        results.append((description, success))
    
    # 显示总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for description, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {description}: {status}")
        if success:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        print("💡 您的 Jina AI 嵌入实现已经可以正常使用了！")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接")
    
    print("\n💡 提示:")
    print("   - 确保设置了正确的 Jina AI API 密钥")
    print("   - 检查网络连接是否正常")
    print("   - 查看详细文档: docs/README_jina_embeddings.md")


if __name__ == "__main__":
    main()
