# 导入必要的库和模块
# Import necessary libraries and modules
from langchain_openai import ChatOpenAI
from jina_embeddings import create_jina_embeddings
import numpy as np

# 配置阿里云通义千问模型
# Configure Alibaba Cloud Qwen model
llm = ChatOpenAI(
    openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1", 
    openai_api_key="sk-282f3112bd714d6e85540da173b5517c", 
    model="qwen-plus-2025-01-25"
)

# 配置Jina嵌入模型
# Configure Jina embeddings model
embeddings = create_jina_embeddings(
    api_key='jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR'
)

print("模型配置完成 / Model configuration completed")
print(f"LLM模型: {llm.model_name}")
print(f"嵌入模型: Jina Embeddings")

class RAG:
    """
    简单的检索增强生成(RAG)系统类
    Simple Retrieval-Augmented Generation (RAG) system class
    
    该类实现了基本的RAG功能：
    - 文档加载和嵌入计算
    - 基于查询的相关文档检索
    - 基于检索文档的答案生成
    """
    
    def __init__(self, llm_model=None, embedding_model=None):
        """
        初始化RAG系统
        Initialize RAG system
        
        Args:
            llm_model: 语言模型实例，如果为None则使用全局配置的llm
            embedding_model: 嵌入模型实例，如果为None则使用全局配置的embeddings
        """
        # 使用传入的模型或全局配置的模型
        # Use provided models or globally configured models
        self.llm = llm_model if llm_model is not None else llm
        self.embeddings = embedding_model if embedding_model is not None else embeddings
        
        # 初始化文档存储
        # Initialize document storage
        self.doc_embeddings = None  # 文档嵌入向量列表
        self.docs = None           # 原始文档列表
        
        print("RAG系统初始化完成 / RAG system initialized")

    def load_documents(self, documents):
        """
        加载文档并计算其嵌入向量
        Load documents and compute their embeddings
        
        Args:
            documents (list): 文档字符串列表
        """
        print(f"正在加载 {len(documents)} 个文档... / Loading {len(documents)} documents...")
        
        # 存储原始文档
        # Store original documents
        self.docs = documents
        
        # 计算文档嵌入向量
        # Compute document embeddings
        self.doc_embeddings = self.embeddings.embed_documents(documents)
        
        print(f"文档加载完成，共计算 {len(self.doc_embeddings)} 个嵌入向量")
        print(f"Document loading completed, computed {len(self.doc_embeddings)} embeddings")

    def get_most_relevant_docs(self, query, top_k=1):
        """
        根据查询找到最相关的文档
        Find the most relevant documents for a given query
        
        Args:
            query (str): 用户查询
            top_k (int): 返回最相关的文档数量，默认为1
            
        Returns:
            list: 最相关的文档列表
        """
        # 检查文档是否已加载
        # Check if documents are loaded
        if not self.docs or not self.doc_embeddings:
            raise ValueError("文档和嵌入向量未加载。请先调用load_documents()方法。")

        print(f"正在检索与查询相关的文档: '{query}'")
        print(f"Retrieving documents relevant to query: '{query}'")
        
        # 计算查询的嵌入向量
        # Compute query embedding
        query_embedding = self.embeddings.embed_query(query)
        
        # 计算余弦相似度
        # Calculate cosine similarity
        similarities = [
            np.dot(query_embedding, doc_emb) / 
            (np.linalg.norm(query_embedding) * np.linalg.norm(doc_emb))
            for doc_emb in self.doc_embeddings
        ]
        
        # 获取最相关的文档索引
        # Get indices of most relevant documents
        top_indices = np.argsort(similarities)[-top_k:][::-1]
        
        # 返回最相关的文档
        # Return most relevant documents
        relevant_docs = [self.docs[i] for i in top_indices]
        
        print(f"找到 {len(relevant_docs)} 个相关文档")
        print(f"Found {len(relevant_docs)} relevant documents")
        
        return relevant_docs

    def generate_answer(self, query, relevant_docs):
        """
        基于最相关的文档生成答案
        Generate an answer based on the most relevant documents
        
        Args:
            query (str): 用户查询
            relevant_docs (list): 相关文档列表
            
        Returns:
            str: 生成的答案
        """
        print("正在生成答案... / Generating answer...")
        
        # 构建提示词
        # Build prompt
        docs_text = "\n\n".join([f"文档{i+1}: {doc}" for i, doc in enumerate(relevant_docs)])
        prompt = f"问题: {query}\n\n相关文档:\n{docs_text}"
        
        # 构建消息
        # Build messages
        messages = [
            ("system", "你是一个有用的助手，只基于给定的文档回答问题。请用中文回答，如果文档中没有相关信息，请明确说明。"),
            ("human", prompt),
        ]
        
        # 调用语言模型生成答案
        # Invoke language model to generate answer
        ai_msg = self.llm.invoke(messages)
        
        print("答案生成完成 / Answer generation completed")
        return ai_msg.content
    
    def query(self, question, top_k=1):
        """
        完整的RAG查询流程：检索 + 生成
        Complete RAG query process: retrieval + generation
        
        Args:
            question (str): 用户问题
            top_k (int): 检索的文档数量
            
        Returns:
            dict: 包含问题、相关文档和答案的字典
        """
        # 检索相关文档
        # Retrieve relevant documents
        relevant_docs = self.get_most_relevant_docs(question, top_k)
        
        # 生成答案
        # Generate answer
        answer = self.generate_answer(question, relevant_docs)
        
        return {
            "question": question,
            "relevant_documents": relevant_docs,
            "answer": answer
        }

# 准备示例文档数据
# Prepare sample document data

sample_docs = [
    "阿尔伯特·爱因斯坦提出了相对论，这一理论彻底改变了我们对时间、空间和重力的理解。相对论包括狭义相对论和广义相对论两部分。",
    "玛丽·居里是一位物理学家和化学家，她在放射性研究方面做出了开创性贡献，并获得了两次诺贝尔奖。她是第一位获得诺贝尔奖的女性。",
    "艾萨克·牛顿制定了运动定律和万有引力定律，为经典力学奠定了基础。牛顿的三大运动定律至今仍是物理学的基石。",
    "查尔斯·达尔文在其著作《物种起源》中提出了自然选择的进化论。这一理论解释了生物物种的多样性和进化过程。",
    "阿达·洛夫莱斯被认为是第一位计算机程序员，她为查尔斯·巴贝奇的早期机械计算机分析机编写了算法。她预见了计算机的巨大潜力。",
    "尼古拉·特斯拉是一位杰出的发明家和电气工程师，他在交流电系统、无线技术和电磁学方面做出了重要贡献。",
    "斯蒂芬·霍金是著名的理论物理学家，他在黑洞理论和宇宙学方面做出了突破性贡献，著有《时间简史》等科普著作。"
]

print(f"准备了 {len(sample_docs)} 个示例文档")
print(f"Prepared {len(sample_docs)} sample documents")
print("\n文档内容预览 / Document content preview:")
for i, doc in enumerate(sample_docs, 1):
    print(f"{i}. {doc[:50]}...")

# 初始化RAG系统实例
# Initialize RAG system instance
print("=" * 50)
print("初始化RAG系统 / Initializing RAG System")
print("=" * 50)

rag = RAG()

# 加载文档到RAG系统
# Load documents into RAG system
print("\n" + "=" * 50)
print("加载文档 / Loading Documents")
print("=" * 50)

rag.load_documents(sample_docs)

# 测试查询1：相对论
# Test query 1: Theory of relativity
print("\n" + "=" * 50)
print("测试查询1 / Test Query 1")
print("=" * 50)

query1 = "谁提出了相对论？"
result1 = rag.query(query1)

print(f"\n问题: {result1['question']}")
print(f"相关文档: {result1['relevant_documents'][0][:100]}...")
print(f"答案: {result1['answer']}")

# 测试查询2：计算机编程
# Test query 2: Computer programming
print("\n" + "=" * 50)
print("测试查询2 / Test Query 2")
print("=" * 50)

query2 = "谁是第一位计算机程序员？"
result2 = rag.query(query2)

print(f"\n问题: {result2['question']}")
print(f"相关文档: {result2['relevant_documents'][0][:100]}...")
print(f"答案: {result2['answer']}")

# 测试查询3：进化论
# Test query 3: Evolution theory
print("\n" + "=" * 50)
print("测试查询3 / Test Query 3")
print("=" * 50)

query3 = "达尔文的主要贡献是什么？"
result3 = rag.query(query3)

print(f"\n问题: {result3['question']}")
print(f"相关文档: {result3['relevant_documents'][0][:100]}...")
print(f"答案: {result3['answer']}")

print("\n" + "=" * 50)
print("RAG系统测试完成 / RAG System Testing Completed")
print("=" * 50)

# RAG系统性能评估和演示
# RAG System Performance Evaluation and Demonstration

print("=" * 60)
print("RAG系统高级功能演示 / Advanced RAG System Features Demo")
print("=" * 60)

# 测试多文档检索
# Test multi-document retrieval
print("\n1. 多文档检索测试 / Multi-document Retrieval Test")
print("-" * 40)

query_multi = "物理学家有哪些重要贡献？"
relevant_docs_multi = rag.get_most_relevant_docs(query_multi, top_k=3)

print(f"查询: {query_multi}")
print(f"检索到 {len(relevant_docs_multi)} 个相关文档:")
for i, doc in enumerate(relevant_docs_multi, 1):
    print(f"  {i}. {doc[:80]}...")

# 生成基于多文档的答案
# Generate answer based on multiple documents
answer_multi = rag.generate_answer(query_multi, relevant_docs_multi)
print(f"\n综合答案: {answer_multi}")

# 测试不相关查询
# Test irrelevant query
print("\n\n2. 不相关查询测试 / Irrelevant Query Test")
print("-" * 40)

irrelevant_query = "今天天气怎么样？"
result_irrelevant = rag.query(irrelevant_query)

print(f"查询: {result_irrelevant['question']}")
print(f"最相关文档: {result_irrelevant['relevant_documents'][0][:80]}...")
print(f"答案: {result_irrelevant['answer']}")

# 系统信息总结
# System information summary
print("\n\n3. 系统配置信息 / System Configuration Info")
print("-" * 40)
print(f"语言模型: 阿里云通义千问 (qwen-plus-2025-01-25)")
print(f"嵌入模型: Jina Embeddings")
print(f"文档数量: {len(rag.docs)}")
print(f"嵌入维度: {len(rag.doc_embeddings[0]) if rag.doc_embeddings else 'N/A'}")
print(f"检索方法: 余弦相似度")
print(f"支持功能: 多文档检索、中文问答、相似度计算")

print("\n" + "=" * 60)
print("演示完成！RAG系统已成功配置并可以使用")
print("Demo completed! RAG system is successfully configured and ready to use")
print("=" * 60)

sample_queries = [
    "Who introduced the theory of relativity?",
    "Who was the first computer programmer?",
    "What did Isaac Newton contribute to science?",
    "Who won two Nobel Prizes for research on radioactivity?",
    "What is the theory of evolution by natural selection?"
]

expected_responses = [
    "Albert Einstein proposed the theory of relativity, which transformed our understanding of time, space, and gravity.",
    "Ada Lovelace is regarded as the first computer programmer for her work on Charles Babbage's early mechanical computer, the Analytical Engine.",
    "Isaac Newton formulated the laws of motion and universal gravitation, laying the foundation for classical mechanics.",
    "Marie Curie was a physicist and chemist who conducted pioneering research on radioactivity and won two Nobel Prizes.",
    "Charles Darwin introduced the theory of evolution by natural selection in his book 'On the Origin of Species'."
]

dataset = []

for query,reference in zip(sample_queries,expected_responses):

    relevant_docs = rag.get_most_relevant_docs(query)
    response = rag.generate_answer(query, relevant_docs)
    dataset.append(
        {
            "user_input":query,
            "retrieved_contexts":relevant_docs,
            "response":response,
            "reference":reference
        }
    )

from ragas import EvaluationDataset
evaluation_dataset = EvaluationDataset.from_list(dataset)

from ragas import evaluate
from ragas.llms import LangchainLLMWrapper


evaluator_llm = LangchainLLMWrapper(llm)
from ragas.metrics import LLMContextRecall, Faithfulness, FactualCorrectness

result = evaluate(dataset=evaluation_dataset,metrics=[LLMContextRecall(), Faithfulness(), FactualCorrectness()],llm=evaluator_llm)
result