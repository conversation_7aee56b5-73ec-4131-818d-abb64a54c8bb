# 导入 RAGAS 库中的核心组件
# SingleTurnSample: 用于创建单轮对话样本的数据结构
# BleuScore: BLEU 评分指标，用于评估文本生成质量
from ragas import SingleTurnSample
from ragas.metrics import BleuScore

# 创建测试数据字典，包含三个关键字段：
test_data = {
    # user_input: 用户输入的原始文本和任务指令
    # 这里的任务是要求对给定文本进行摘要
    "user_input": "summarise given text\nThe company reported an 8% rise in Q3 2024, driven by strong performance in the Asian market. Sales in this region have significantly contributed to the overall growth. Analysts attribute this success to strategic marketing and product localization. The positive trend in the Asian market is expected to continue into the next quarter.",
    
    # response: AI 模型生成的响应/摘要
    # 这是需要被评估的目标文本
    "response": "The company experienced an 8% increase in Q3 2024, largely due to effective marketing strategies and product adaptation, with expectations of continued growth in the coming quarter.",
    
    # reference: 参考答案/标准答案
    # 用作评估 response 质量的基准
    "reference": "The company reported an 8% growth in Q3 2024, primarily driven by strong sales in the Asian market, attributed to strategic marketing and localized products, with continued growth anticipated in the next quarter."
}

# 初始化 BLEU 评分指标
# BLEU (Bilingual Evaluation Understudy) 是一种常用的文本质量评估指标
# 主要通过计算 n-gram 重叠度来评估生成文本与参考文本的相似性
metric = BleuScore()

# 将测试数据转换为 SingleTurnSample 对象
# 这是 RAGAS 库要求的标准数据格式
test_data = SingleTurnSample(**test_data)

# 计算单轮对话的 BLEU 评分
# 返回值是一个 0-1 之间的浮点数，越接近 1 表示质量越好
# 这里的结果约为 0.137，表明生成的摘要与参考答案有一定相似性但还有改进空间
metric.single_turn_score(test_data)

from langchain_openai import ChatOpenAI
llm = ChatOpenAI(openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1", 
                 openai_api_key="sk-282f3112bd714d6e85540da173b5517c", model="qwen-plus-2025-01-25")

from ragas.llms import LangchainLLMWrapper
from ragas.embeddings import LangchainEmbeddingsWrapper
# from langchain_openai import OpenAIEmbeddings
from jina_embeddings import create_jina_embeddings
evaluator_llm = LangchainLLMWrapper(llm)
# evaluator_embeddings = LangchainEmbeddingsWrapper(OpenAIEmbeddings())
evaluator_embeddings = LangchainEmbeddingsWrapper(create_jina_embeddings(api_key = 'jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR'))

from ragas import SingleTurnSample
from ragas.metrics import AspectCritic

test_data = {
    "user_input": "summarise given text\nThe company reported an 8% rise in Q3 2024, driven by strong performance in the Asian market. Sales in this region have significantly contributed to the overall growth. Analysts attribute this success to strategic marketing and product localization. The positive trend in the Asian market is expected to continue into the next quarter.",
    "response": "The company experienced an 8% increase in Q3 2024, largely due to effective marketing strategies and product adaptation, with expectations of continued growth in the coming quarter.",
}

metric = AspectCritic(name="summary_accuracy",llm=evaluator_llm, definition="Verify if the summary is accurate.")
test_data = SingleTurnSample(**test_data)
await metric.single_turn_ascore(test_data)

from datasets import load_dataset
from ragas import EvaluationDataset
eval_dataset = load_dataset("explodinggradients/earning_report_summary",split="train")
eval_dataset = EvaluationDataset.from_hf_dataset(eval_dataset)
print("Features in dataset:", eval_dataset.features())
print("Total samples in dataset:", len(eval_dataset))

# 使用数据集进行评估

from ragas import evaluate

results = evaluate(eval_dataset, metrics=[metric])
results



results.to_pandas()