"""
Jina AI 嵌入模型的自定义实现
支持 RAGAS 框架的嵌入接口，使用 Jina AI 提供的 API 服务
"""

import asyncio
import logging
import time
from typing import List, Optional, Dict, Any
import httpx
from ragas.embeddings.base import BaseRagasEmbeddings
from ragas.run_config import RunConfig

# 配置日志
logger = logging.getLogger(__name__)


class JinaEmbeddings(BaseRagasEmbeddings):
    """
    Jina AI 嵌入模型的自定义实现类
    
    这个类继承自 BaseRagasEmbeddings，实现了使用 Jina AI API 进行文本嵌入的功能。
    支持同步和异步操作，适用于 RAGAS 框架的评估和测试数据生成。
    
    属性:
        api_key (str): Jina AI 的 API 密钥
        model_name (str): 使用的嵌入模型名称
        base_url (str): API 基础 URL
        max_retries (int): 最大重试次数
        timeout (int): 请求超时时间（秒）
        batch_size (int): 批处理大小
    """
    
    def __init__(
        self,
        api_key: str,
        model_name: str = "jina-embeddings-v3",
        base_url: str = "https://api.jina.ai/v1/embeddings",
        task: str = "retrieval.query",
        max_retries: int = 3,
        timeout: int = 30,
        batch_size: int = 100,
        run_config: Optional[RunConfig] = None,
        **kwargs
    ):
        """
        初始化 Jina AI 嵌入模型

        参数:
            api_key: Jina AI 的 API 密钥
            model_name: 嵌入模型名称，默认为 "jina-embeddings-v3"
            base_url: API 基础 URL
            task: 任务类型，默认为 "retrieval.query"
            max_retries: 最大重试次数
            timeout: 请求超时时间
            batch_size: 批处理大小
            run_config: 运行配置
            **kwargs: 其他参数
        """
        super().__init__(**kwargs)

        self.api_key = api_key
        self.model_name = model_name
        self.base_url = base_url
        self.task = task
        self.max_retries = max_retries
        self.timeout = timeout
        self.batch_size = batch_size
        
        # 设置运行配置
        if run_config is None:
            run_config = RunConfig()
        self.set_run_config(run_config)
        
        # 验证 API 密钥
        if not self.api_key:
            raise ValueError("API 密钥不能为空")
        
        logger.info(f"初始化 Jina AI 嵌入模型: {self.model_name}")

    def _prepare_headers(self) -> Dict[str, str]:
        """准备请求头"""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

    def _prepare_payload(self, texts: List[str]) -> Dict[str, Any]:
        """准备请求负载"""
        return {
            "model": self.model_name,
            "task": self.task,
            "input": texts
        }

    def _handle_response(self, response_data: Dict[str, Any]) -> List[List[float]]:
        """处理 API 响应"""
        try:
            if "data" not in response_data:
                raise ValueError(f"API 响应格式错误: {response_data}")
            
            embeddings = []
            for item in response_data["data"]:
                if "embedding" not in item:
                    raise ValueError(f"嵌入数据格式错误: {item}")
                embeddings.append(item["embedding"])
            
            return embeddings
        except Exception as e:
            logger.error(f"处理响应时出错: {e}")
            raise

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        同步嵌入多个文档
        
        参数:
            texts: 要嵌入的文本列表
            
        返回:
            嵌入向量列表
        """
        if not texts:
            return []
        
        logger.info(f"开始嵌入 {len(texts)} 个文档")
        
        all_embeddings = []
        
        # 分批处理
        for i in range(0, len(texts), self.batch_size):
            batch_texts = texts[i:i + self.batch_size]
            batch_embeddings = self._embed_batch_sync(batch_texts)
            all_embeddings.extend(batch_embeddings)
        
        logger.info(f"完成文档嵌入，共 {len(all_embeddings)} 个向量")
        return all_embeddings

    def _embed_batch_sync(self, texts: List[str]) -> List[List[float]]:
        """同步嵌入一批文本"""
        headers = self._prepare_headers()
        payload = self._prepare_payload(texts)
        
        for attempt in range(self.max_retries):
            try:
                with httpx.Client(timeout=self.timeout) as client:
                    response = client.post(
                        self.base_url,
                        headers=headers,
                        json=payload
                    )
                    response.raise_for_status()
                    
                    response_data = response.json()
                    return self._handle_response(response_data)
                    
            except httpx.HTTPStatusError as e:
                logger.warning(f"HTTP 错误 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt == self.max_retries - 1:
                    raise
                time.sleep(2 ** attempt)  # 指数退避
                
            except Exception as e:
                logger.error(f"嵌入请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt == self.max_retries - 1:
                    raise
                time.sleep(2 ** attempt)

    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        异步嵌入多个文档
        
        参数:
            texts: 要嵌入的文本列表
            
        返回:
            嵌入向量列表
        """
        if not texts:
            return []
        
        logger.info(f"开始异步嵌入 {len(texts)} 个文档")
        
        all_embeddings = []
        
        # 分批处理
        for i in range(0, len(texts), self.batch_size):
            batch_texts = texts[i:i + self.batch_size]
            batch_embeddings = await self._embed_batch_async(batch_texts)
            all_embeddings.extend(batch_embeddings)
        
        logger.info(f"完成异步文档嵌入，共 {len(all_embeddings)} 个向量")
        return all_embeddings

    async def _embed_batch_async(self, texts: List[str]) -> List[List[float]]:
        """异步嵌入一批文本"""
        headers = self._prepare_headers()
        payload = self._prepare_payload(texts)
        
        for attempt in range(self.max_retries):
            try:
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    response = await client.post(
                        self.base_url,
                        headers=headers,
                        json=payload
                    )
                    response.raise_for_status()
                    
                    response_data = response.json()
                    return self._handle_response(response_data)
                    
            except httpx.HTTPStatusError as e:
                logger.warning(f"异步 HTTP 错误 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt == self.max_retries - 1:
                    raise
                await asyncio.sleep(2 ** attempt)  # 指数退避
                
            except Exception as e:
                logger.error(f"异步嵌入请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt == self.max_retries - 1:
                    raise
                await asyncio.sleep(2 ** attempt)

    def embed_query(self, text: str) -> List[float]:
        """
        同步嵌入单个查询文本
        
        参数:
            text: 要嵌入的文本
            
        返回:
            嵌入向量
        """
        embeddings = self.embed_documents([text])
        return embeddings[0] if embeddings else []

    async def aembed_query(self, text: str) -> List[float]:
        """
        异步嵌入单个查询文本
        
        参数:
            text: 要嵌入的文本
            
        返回:
            嵌入向量
        """
        embeddings = await self.aembed_documents([text])
        return embeddings[0] if embeddings else []


def create_jina_embeddings(
    api_key: str,
    model_name: str = "jina-embeddings-v3",
    **kwargs
) -> JinaEmbeddings:
    """
    创建 Jina AI 嵌入实例的工厂函数
    
    参数:
        api_key: Jina AI 的 API 密钥
        model_name: 嵌入模型名称
        **kwargs: 其他参数
        
    返回:
        JinaEmbeddings 实例
    """
    return JinaEmbeddings(
        api_key=api_key,
        model_name=model_name,
        **kwargs
    )
